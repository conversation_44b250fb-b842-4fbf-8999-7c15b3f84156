import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';
import Home from './components/Home';
import JobDetail from './components/JobDetail';
import AdminLogin from './components/AdminLogin';
import AdminDashboard from './components/AdminDashboard';
import SectionDetailsAnalysis from './components/SectionDetailsAnalysis';
import VisitorDetails from './components/VisitorDetails';
import AllVisitorsDetails from './components/AllVisitorsDetails';
import ExperienceProjectsAnalytics from './components/ExperienceProjectsAnalytics';
import PortfolioProjectsAnalytics from './components/PortfolioProjectsAnalytics';
import ExtensionErrorTester from './components/ExtensionErrorTester';
import ScrollToTopButton from './components/ScrollToTopButton';
import GeolocationTester from './components/GeolocationTester';
import { initializeBackendWakeup } from './utils/backendWakeup';
import { initializeExtensionErrorHandling, filterExtensionConsoleErrors } from './utils/extensionErrorHandler';
import {
  initializeAdvancedExtensionInterception,
  protectCriticalAPIs
} from './utils/extensionErrorInterceptor';

function App() {
  // Initialize backend wakeup and extension error handling when app loads
  useEffect(() => {
    // Initialize all extension error protection layers
    initializeExtensionErrorHandling(); // Basic extension error handling
    filterExtensionConsoleErrors(); // Filter console spam from extensions
    initializeAdvancedExtensionInterception(); // Advanced interception
    protectCriticalAPIs(); // Protect localStorage, sessionStorage, etc.

    // Initialize backend wakeup after error handling is set up
    initializeBackendWakeup();
  }, []); // Empty dependency array means this runs once on mount

  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/job/:slug" element={<JobDetail />} />
          <Route path="/admin/login" element={<AdminLogin />} />
          <Route path="/admin/dashboard" element={<AdminDashboard />} />
          <Route path="/admin/section-details" element={<SectionDetailsAnalysis />} />
          <Route path="/admin/visitor/:visitorIp" element={<VisitorDetails />} />
          <Route path="/admin/all-visitors" element={<AllVisitorsDetails />} />
          <Route path="/admin/experience-analytics" element={<ExperienceProjectsAnalytics />} />
          <Route path="/admin/portfolio-analytics" element={<PortfolioProjectsAnalytics />} />
          <Route path="/admin/extension-tester" element={<ExtensionErrorTester />} />
          <Route path="/admin/geolocation-tester" element={<GeolocationTester />} />
        </Routes>
        <ScrollToTopButton />
      </div>
    </Router>
  );
}

export default App;
