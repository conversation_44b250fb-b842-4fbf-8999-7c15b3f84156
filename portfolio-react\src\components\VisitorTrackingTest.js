import React, { useState, useEffect } from 'react';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch } from '../utils/extensionErrorHandler';

const VisitorTrackingTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const runTrackingTest = async () => {
    setIsLoading(true);
    const results = [];

    try {
      // Test 1: Check API configuration
      results.push({
        test: 'API Configuration',
        status: API_CONFIG.BASE_URL ? 'PASS' : 'FAIL',
        details: `Base URL: ${API_CONFIG.BASE_URL}`
      });

      // Test 2: Test tracking endpoint
      try {
        const trackResponse = await safeFetch(API_CONFIG.ENDPOINTS.TRACK_VISIT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            section: 'test-section',
            duration: 5,
            sessionId: 'test-session-' + Date.now(),
            pageUrl: window.location.href
          }),
        });

        if (trackResponse.ok) {
          results.push({
            test: 'Track Visit Endpoint',
            status: 'PASS',
            details: 'Successfully sent tracking data'
          });
        } else {
          results.push({
            test: 'Track Visit Endpoint',
            status: 'FAIL',
            details: `HTTP ${trackResponse.status}: ${trackResponse.statusText}`
          });
        }
      } catch (error) {
        results.push({
          test: 'Track Visit Endpoint',
          status: 'FAIL',
          details: `Error: ${error.message}`
        });
      }

      // Test 3: Check dashboard endpoint (requires auth)
      try {
        const dashboardResponse = await safeFetch(API_CONFIG.ENDPOINTS.ADMIN_DASHBOARD, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token') || 'no-token'}`
          }
        });

        if (dashboardResponse.ok) {
          const data = await dashboardResponse.json();
          results.push({
            test: 'Dashboard Data',
            status: 'PASS',
            details: `Total visits: ${data.totalVisits}, Today visitors: ${data.dailyStats?.todayVisitors || 0}${data.debug ? `, Tunisia date: ${data.debug.tunisiaDate}` : ''}`
          });
        } else {
          results.push({
            test: 'Dashboard Data',
            status: 'FAIL',
            details: `HTTP ${dashboardResponse.status}: ${dashboardResponse.statusText}`
          });
        }
      } catch (error) {
        results.push({
          test: 'Dashboard Data',
          status: 'FAIL',
          details: `Error: ${error.message}`
        });
      }

      // Test 4: Timezone Test (requires auth)
      try {
        const timezoneResponse = await safeFetch(API_CONFIG.ENDPOINTS.TIMEZONE_TEST, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token') || 'no-token'}`
          }
        });

        if (timezoneResponse.ok) {
          const data = await timezoneResponse.json();
          results.push({
            test: 'Timezone Test',
            status: 'PASS',
            details: `Server: ${data.serverInfo.serverTime.substring(0, 19)}, Tunisia: ${data.serverInfo.tunisiaTime}, Today visits: ${data.visitCounts.todayVisitsCount} (${data.visitCounts.todayUniqueIPs} unique)`
          });
        } else {
          results.push({
            test: 'Timezone Test',
            status: 'FAIL',
            details: `HTTP ${timezoneResponse.status}: ${timezoneResponse.statusText}`
          });
        }
      } catch (error) {
        results.push({
          test: 'Timezone Test',
          status: 'FAIL',
          details: `Error: ${error.message}`
        });
      }

      // Test 5: Check browser environment
      results.push({
        test: 'Browser Environment',
        status: 'INFO',
        details: `User Agent: ${navigator.userAgent.substring(0, 100)}...`
      });

      results.push({
        test: 'Current URL',
        status: 'INFO',
        details: window.location.href
      });

      results.push({
        test: 'Local Storage Token',
        status: localStorage.getItem('token') ? 'PASS' : 'FAIL',
        details: localStorage.getItem('token') ? 'Token exists' : 'No token found'
      });

    } catch (error) {
      results.push({
        test: 'General Error',
        status: 'FAIL',
        details: error.message
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  useEffect(() => {
    // Auto-run test on component mount
    runTrackingTest();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#1a1a1a', 
      color: '#fff', 
      fontFamily: 'monospace',
      minHeight: '100vh'
    }}>
      <h1>🔍 Visitor Tracking Test</h1>
      
      <button 
        onClick={runTrackingTest} 
        disabled={isLoading}
        style={{
          padding: '10px 20px',
          backgroundColor: '#4B0082',
          color: '#fff',
          border: 'none',
          borderRadius: '5px',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          marginBottom: '20px'
        }}
      >
        {isLoading ? 'Running Tests...' : 'Run Tests'}
      </button>

      <div>
        {testResults.map((result, index) => (
          <div 
            key={index}
            style={{
              padding: '10px',
              margin: '10px 0',
              backgroundColor: result.status === 'PASS' ? '#2d5a2d' : 
                              result.status === 'FAIL' ? '#5a2d2d' : '#2d4a5a',
              borderRadius: '5px',
              border: `2px solid ${result.status === 'PASS' ? '#4CAF50' : 
                                  result.status === 'FAIL' ? '#f44336' : '#2196F3'}`
            }}
          >
            <strong>{result.test}</strong>: 
            <span style={{ 
              color: result.status === 'PASS' ? '#4CAF50' : 
                     result.status === 'FAIL' ? '#f44336' : '#2196F3',
              marginLeft: '10px'
            }}>
              {result.status}
            </span>
            <div style={{ marginTop: '5px', fontSize: '0.9em', opacity: 0.8 }}>
              {result.details}
            </div>
          </div>
        ))}
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#333', borderRadius: '5px' }}>
        <h3>📋 Instructions:</h3>
        <ol>
          <li>This test checks if visitor tracking is working properly</li>
          <li>It tests the API endpoints and configuration</li>
          <li>If tracking fails, check the network tab in browser dev tools</li>
          <li>Make sure the backend server is running and accessible</li>
          <li>For dashboard data test, you need to be logged in as admin</li>
        </ol>
      </div>
    </div>
  );
};

export default VisitorTrackingTest;
